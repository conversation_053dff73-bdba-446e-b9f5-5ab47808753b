# Authentication Integration

This document describes the authentication system implemented for the Gigsta mobile app using Supabase Auth.

## Overview

The authentication system provides:
- User registration (sign up)
- User login (sign in)
- User logout (sign out)
- Session management
- Automatic authentication state handling

## Architecture

### Domain Layer
- **User**: Domain model representing a user
- **AuthState**: Sealed class representing authentication states (Loading, Authenticated, Unauthenticated, Error)
- **AuthRepository**: Interface defining authentication operations
- **Use Cases**: SignInUseCase, SignUpUseCase, SignOutUseCase with validation logic

### Data Layer
- **AuthRepositoryImpl**: Implementation using Supabase Auth
- **SupabaseClient**: Updated to include Auth module

### Presentation Layer
- **AuthViewModel**: Manages authentication state and UI interactions
- **AuthScreen**: Login/signup form with validation and error handling
- **App**: Main component that routes between AuthScreen and HomeScreen based on auth state

## Features

### AuthScreen
- Toggle between login and signup modes
- Email and password validation
- Password visibility toggle
- Loading states and error handling
- Responsive design with Material 3 components

### Session Management
- Automatic session restoration on app launch
- Real-time authentication state updates
- Secure token handling through Supabase

### Integration with Existing Features
- HistoryRepository now uses authenticated user ID instead of hardcoded value
- HomeScreen includes logout functionality
- Seamless transition between authenticated and unauthenticated states

## Usage

### Sign Up
1. User opens the app
2. If not authenticated, AuthScreen is shown
3. User toggles to "Sign Up" mode
4. Enters email and password (minimum 6 characters)
5. Submits form
6. On success, automatically signed in and redirected to HomeScreen

### Sign In
1. User enters email and password
2. Submits form
3. On success, redirected to HomeScreen
4. On failure, error message is displayed

### Sign Out
1. User taps the logout icon in HomeScreen top bar
2. Session is cleared
3. User is redirected to AuthScreen

## Configuration

The authentication system uses the existing Supabase configuration in `NetworkConfig.kt`. Make sure your Supabase project has:

1. **Auth enabled** in your Supabase dashboard
2. **Email provider configured** (default is enabled)
3. **Proper RLS policies** for your data tables

## Security

- Passwords are handled securely by Supabase Auth
- Session tokens are automatically managed
- User data is only accessible when authenticated
- All API calls use the authenticated user's session

## Error Handling

The system handles various error scenarios:
- Invalid email format
- Weak passwords
- Network errors
- Authentication failures
- Session expiration

## Next Steps

1. **Email Verification**: Add email confirmation flow
2. **Password Reset**: Implement forgot password functionality
3. **Social Auth**: Add Google/Apple sign-in options
4. **Profile Management**: Add user profile editing
5. **Offline Support**: Handle authentication when offline

## Dependencies Added

```toml
# In gradle/libs.versions.toml
supabase-auth-kt = { module = "io.github.jan-tennert.supabase:auth-kt", version.ref = "supabase" }
```

## File Structure

```
mobile/composeApp/src/commonMain/kotlin/io/gigsta/
├── data/repository/
│   └── AuthRepositoryImpl.kt (new)
├── domain/
│   ├── model/
│   │   ├── User.kt (new)
│   │   └── AuthState.kt (new)
│   ├── repository/
│   │   └── AuthRepository.kt (new)
│   └── usecase/
│       ├── SignInUseCase.kt (new)
│       ├── SignUpUseCase.kt (new)
│       └── SignOutUseCase.kt (new)
├── presentation/
│   └── auth/
│       ├── AuthViewModel.kt (new)
│       └── AuthScreen.kt (new)
├── di/
│   └── AppModule.kt (updated)
├── data/
│   ├── network/
│   │   └── SupabaseClient.kt (updated)
│   └── repository/
│       └── HistoryRepositoryImpl.kt (updated)
└── App.kt (updated)
```
