package io.gigsta.presentation.auth

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.gigsta.domain.model.AuthState
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.domain.usecase.SignInUseCase
import io.gigsta.domain.usecase.SignUpUseCase
import io.gigsta.domain.usecase.SignOutUseCase
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class AuthViewModel(
    private val authRepository: AuthRepository,
    private val signInUseCase: SignInUseCase,
    private val signUpUseCase: SignUpUseCase,
    private val signOutUseCase: SignOutUseCase
) : ViewModel() {
    
    var uiState by mutableStateOf(AuthUiState())
        private set
    
    var authState by mutableStateOf<AuthState>(AuthState.Loading)
        private set
    
    init {
        observeAuthState()
    }
    
    private fun observeAuthState() {
        viewModelScope.launch {
            authRepository.authState.collectLatest { state ->
                authState = state
            }
        }
    }
    
    fun onEmailChanged(email: String) {
        uiState = uiState.copy(email = email, error = null)
    }
    
    fun onPasswordChanged(password: String) {
        uiState = uiState.copy(password = password, error = null)
    }
    
    fun onToggleAuthMode() {
        uiState = uiState.copy(
            isSignUp = !uiState.isSignUp,
            error = null
        )
    }
    
    fun onSignIn() {
        if (uiState.isLoading) return
        
        viewModelScope.launch {
            uiState = uiState.copy(isLoading = true, error = null)
            
            val result = signInUseCase(uiState.email, uiState.password)
            
            result.fold(
                onSuccess = {
                    uiState = uiState.copy(isLoading = false)
                },
                onFailure = { exception ->
                    uiState = uiState.copy(
                        isLoading = false,
                        error = exception.message ?: "Sign in failed"
                    )
                }
            )
        }
    }
    
    fun onSignUp() {
        if (uiState.isLoading) return
        
        viewModelScope.launch {
            uiState = uiState.copy(isLoading = true, error = null)
            
            val result = signUpUseCase(uiState.email, uiState.password)
            
            result.fold(
                onSuccess = {
                    uiState = uiState.copy(isLoading = false)
                },
                onFailure = { exception ->
                    uiState = uiState.copy(
                        isLoading = false,
                        error = exception.message ?: "Sign up failed"
                    )
                }
            )
        }
    }
    
    fun onSignOut() {
        viewModelScope.launch {
            signOutUseCase()
        }
    }
}

data class AuthUiState(
    val email: String = "",
    val password: String = "",
    val isSignUp: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null
)
