package io.gigsta.domain.model

sealed interface BaseHistoryItem {
    val id: String
    val type: HistoryItemType
    val createdAt: String
}

data class ResumeHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.CV_BUILDER,
    override val createdAt: String,
    val structuredData: String?,
    val htmlContent: String?,
    val templateId: String,
    val templateName: String?,
    val tokensDeducted: Boolean
) : BaseHistoryItem

data class LetterHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.APPLICATION_LETTER,
    override val createdAt: String,
    val templateId: String,
    val templateName: String?,
    val plainText: String?,
    val designHtml: String?,
) : BaseHistoryItem

data class EmailHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.EMAIL_APPLICATION,
    override val createdAt: String,
    val subject: String,
    val body: String
) : BaseHistoryItem

data class PersonalInfo(
    val fullName: String? = null,
    val email: String? = null,
    val phone: String? = null
)

enum class HistoryItemType {
    CV_BUILDER,
    APPLICATION_LETTER,
    EMAIL_APPLICATION,
}