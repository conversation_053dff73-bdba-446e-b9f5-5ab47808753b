package io.gigsta.domain.usecase

import io.gigsta.domain.model.User
import io.gigsta.domain.repository.AuthRepository

class SignInUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(email: String, password: String): Result<User> {
        if (email.isBlank()) {
            return Result.failure(Exception("Email cannot be empty"))
        }
        if (password.isBlank()) {
            return Result.failure(Exception("Password cannot be empty"))
        }
        if (!isValidEmail(email)) {
            return Result.failure(Exception("Please enter a valid email address"))
        }
        
        return authRepository.signIn(email, password)
    }
    
    private fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {
        return email.contains("@") && email.contains(".")
    }
}
