package io.gigsta.domain.repository

import io.gigsta.domain.model.BaseHistoryItem
import io.gigsta.domain.model.HistoryItemType

interface HistoryRepository {
    suspend fun getHistoryItems(type: HistoryItemType): List<BaseHistoryItem>
    suspend fun createHistoryItem(item: BaseHistoryItem): Result<BaseHistoryItem>
    suspend fun updateHistoryItem(item: BaseHistoryItem): Result<BaseHistoryItem>
    suspend fun deleteHistoryItem(id: String): Result<Unit>
}
