package io.gigsta.domain.utils

data class ResumeTemplate(
    val id: String,
    val name: String,
    val previewDescription: String,
    val recommended: Boolean,
    val tokenCost: Int,
    val fontFamily: String
)

val cleanProfessionalTemplate = ResumeTemplate(
    id = "clean-professional",
    name = "Clean Professional",
    previewDescription = "Template resume bersih dan profesional untuk semua industri",
    tokenCost = 15,
    recommended = true,
    fontFamily = "Arial, sans-serif"
)

val modernCleanTemplate = ResumeTemplate(
    id = "modern-clean",
    name = "Modern Clean",
    previewDescription = "Template resume minimalis dengan desain modern dan elegan untuk profesional",
    tokenCost = 15,
    recommended = false,
    fontFamily = "Arial, sans-serif"
)

val classicProfessionalTemplate = ResumeTemplate(
    id = "classic-professional",
    name = "Classic Professional",
    previewDescription = "Template resume klasik dengan format standar yang mudah dibaca dan maks<PERSON><PERSON><PERSON> peluang lolos screening otomatis",
    tokenCost = 15,
    recommended = true,
    fontFamily = "Arial, sans-serif"
)

val minimalistProfessionalTemplate = ResumeTemplate(
    id = "minimalist-professional",
    name = "Minimalist Professional",
    previewDescription = "Template resume minimalis dengan desain bersih dan elegan untuk profesional yang mengutamakan kesederhanaan",
    tokenCost = 15,
    recommended = false,
    fontFamily = "Arial, sans-serif"
)

val executiveProfessionalTemplate = ResumeTemplate(
    id = "executive-professional",
    name = "Executive Professional",
    previewDescription = "Template resume eksekutif dengan desain profesional dan layout yang kuat untuk posisi senior dan manajemen",
    tokenCost = 15,
    recommended = true,
    fontFamily = "Arial, sans-serif"
)

val corporateStandardTemplate = ResumeTemplate(
    id = "corporate-standard",
    name = "Corporate Standard",
    previewDescription = "Template resume standar korporat dengan format tradisional yang sangat ATS-friendly dan mudah dibaca oleh sistem otomatis",
    tokenCost = 15,
    recommended = true,
    fontFamily = "Times New Roman, serif"
)

val resumeTemplates = listOf(
    cleanProfessionalTemplate,
    modernCleanTemplate,
    classicProfessionalTemplate,
    minimalistProfessionalTemplate,
    executiveProfessionalTemplate,
    corporateStandardTemplate
)

fun getResumeTemplateById(id: String): ResumeTemplate? {
    return resumeTemplates.find { template -> template.id == id }
}

fun getAllResumeTemplates(): List<ResumeTemplate> {
    return resumeTemplates
}