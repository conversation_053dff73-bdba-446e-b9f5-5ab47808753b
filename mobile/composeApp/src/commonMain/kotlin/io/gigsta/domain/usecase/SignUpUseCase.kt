package io.gigsta.domain.usecase

import io.gigsta.domain.model.User
import io.gigsta.domain.repository.AuthRepository

class SignUpUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(email: String, password: String): Result<User> {
        if (email.isBlank()) {
            return Result.failure(Exception("Email cannot be empty"))
        }
        if (password.isBlank()) {
            return Result.failure(Exception("Password cannot be empty"))
        }
        if (!isValidEmail(email)) {
            return Result.failure(Exception("Please enter a valid email address"))
        }
        if (password.length < 6) {
            return Result.failure(Exception("Password must be at least 6 characters long"))
        }
        
        return authRepository.signUp(email, password)
    }
    
    private fun isValidEmail(email: String): <PERSON><PERSON>an {
        return email.contains("@") && email.contains(".")
    }
}
