package io.gigsta.domain.utils

import kotlinx.serialization.Serializable

/**
 * Letter template data classes
 * Kotlin equivalent of web/types/letter-structured.ts interfaces
 */

@Serializable
data class LetterMetadata(
    val generatedAt: String,
    val lastModified: String,
    val templateId: String,
    val language: String = "id" // "id" or "en"
)

@Serializable
data class LetterHeader(
    val date: String,
    val formattedDate: String? = null
)

@Serializable
data class LetterSubject(
    val prefix: String,
    val position: String
)

@Serializable
data class LetterRecipient(
    val salutation: String,
    val title: String,
    val company: String? = null,
    val address: List<String>? = null
)

@Serializable
data class LetterBody(
    val opening: String,
    val paragraphs: List<String>,
    val closing: String
)

@Serializable
data class LetterSignature(
    val farewell: String,
    val name: String
)

@Serializable
data class StructuredLetterData(
    val metadata: LetterMetadata,
    val header: LetterHeader,
    val subject: LetterSubject,
    val recipient: LetterRecipient,
    val body: LetterBody,
    val signature: LetterSignature
)

@Serializable
data class LetterTemplateData(
    val date: String,
    val subject: String,
    val recipientLines: List<String>,
    val salutation: String,
    val opening: String,
    val paragraphs: List<String>,
    val closing: String,
    val farewell: String,
    val signatureName: String,
    val additionalInfo: String? = null,
    val templateId: String,
    val language: String
)

/**
 * Letter template definition
 * Mobile equivalent of web template system
 */
data class LetterTemplate(
    val id: String,
    val name: String,
    val previewDescription: String,
    val previewImagePath: String,
    val isPremium: Boolean,
    val recommended: Boolean,
    val tokenCost: Int,
    val fontFamily: String
)

// Template definitions - mobile equivalents of web templates
val plainTextLetterTemplate = LetterTemplate(
    id = "plain-text",
    name = "Plain Text",
    previewDescription = "Surat lamaran dengan format teks sederhana",
    previewImagePath = "/svgs/plain_text.svg",
    isPremium = false,
    tokenCost = 10,
    recommended = false,
    fontFamily = "Roboto, sans-serif"
)

val classicBlueLetterTemplate = LetterTemplate(
    id = "classic-blue",
    name = "Classic Blue",
    previewDescription = "Surat formal dengan aksen biru dan layout profesional",
    previewImagePath = "/svgs/classic_blue.svg",
    isPremium = true,
    tokenCost = 15,
    recommended = false,
    fontFamily = "Merriweather, serif"
)

val professionalClassicLetterTemplate = LetterTemplate(
    id = "professional-classic",
    name = "Professional Classic",
    previewDescription = "Desain profesional dengan header biru dan judul tercentrasi",
    previewImagePath = "/svgs/professional_classic.svg",
    isPremium = true,
    tokenCost = 15,
    recommended = true,
    fontFamily = "Inter, sans-serif"
)

val minimalistSidebarLetterTemplate = LetterTemplate(
    id = "minimalist-sidebar",
    name = "Minimalist Sidebar",
    previewDescription = "Desain modern dengan sidebar indigo di sisi kiri, tata letak premium, dan spasi bersih",
    previewImagePath = "/svgs/minimalist_sidebar.svg",
    isPremium = true,
    tokenCost = 15,
    recommended = false,
    fontFamily = "Source Sans Pro, sans-serif"
)

val minimalistBorderFrameLetterTemplate = LetterTemplate(
    id = "minimalist-border-frame",
    name = "Minimalist Border Frame",
    previewDescription = "Desain elegan dengan bingkai border, detail sudut halus berwarna hijau, dan hierarki baris yang rapi",
    previewImagePath = "/svgs/minimalist_border.svg",
    isPremium = true,
    tokenCost = 15,
    recommended = false,
    fontFamily = "Lato, sans-serif"
)

val minimalistAccentLetterTemplate = LetterTemplate(
    id = "minimalist-accent",
    name = "Minimalist Accents",
    previewDescription = "Desain modern dan bersih dengan aksen kuning, spasi teratur, dan elemen dekoratif minimalis",
    previewImagePath = "/svgs/minimalist_accent.svg",
    isPremium = true,
    tokenCost = 15,
    recommended = false,
    fontFamily = "IBM Plex Sans, sans-serif"
)

val minimalistCircularAccentsLetterTemplate = LetterTemplate(
    id = "minimalist-circular-accents",
    name = "Minimalist Circular Accents",
    previewDescription = "Desain modern dengan elemen aksen lingkaran ungu, spasi yang bersih, dan pola titik yang halus",
    previewImagePath = "/svgs/minimalist_circular_accent.svg",
    isPremium = true,
    tokenCost = 15,
    recommended = false,
    fontFamily = "Open Sans, sans-serif"
)

// Collection of all available letter templates
val applicationLetterTemplates = listOf(
    plainTextLetterTemplate,
    classicBlueLetterTemplate,
    professionalClassicLetterTemplate,
    minimalistSidebarLetterTemplate,
    minimalistBorderFrameLetterTemplate,
    minimalistAccentLetterTemplate,
    minimalistCircularAccentsLetterTemplate
)

/**
 * Get letter template by ID
 */
fun getLetterTemplateById(id: String): LetterTemplate? {
    return applicationLetterTemplates.find { template -> template.id == id }
}

/**
 * Get all letter templates
 */
fun getAllLetterTemplates(): List<LetterTemplate> {
    return applicationLetterTemplates
}

/**
 * Get only premium letter templates
 */
fun getPremiumLetterTemplates(): List<LetterTemplate> {
    return applicationLetterTemplates.filter { it.isPremium }
}

/**
 * Get only free letter templates
 */
fun getFreeLetterTemplates(): List<LetterTemplate> {
    return applicationLetterTemplates.filter { !it.isPremium }
}

/**
 * Get recommended letter templates
 */
fun getRecommendedLetterTemplates(): List<LetterTemplate> {
    return applicationLetterTemplates.filter { it.recommended }
}

/**
 * Convert structured letter data to template-compatible format
 * Kotlin equivalent of convertToLetterTemplateData from web
 */
fun convertToLetterTemplateData(data: StructuredLetterData): LetterTemplateData {
    // Build recipient lines array
    val recipientLines = mutableListOf<String>()
    
    if (data.recipient.salutation.isNotEmpty() && data.recipient.title.isNotEmpty()) {
        recipientLines.add("${data.recipient.salutation} ${data.recipient.title}")
    }
    
    data.recipient.company?.let { company ->
        if (company.isNotEmpty()) {
            recipientLines.add(company)
        }
    }
    
    data.recipient.address?.let { address ->
        recipientLines.addAll(address)
    }
    
    // Combine subject prefix and position
    val subject = if (data.subject.prefix.isNotEmpty()) {
        "${data.subject.prefix} ${data.subject.position}"
    } else {
        data.subject.position
    }
    
    return LetterTemplateData(
        // Date
        date = data.header.formattedDate ?: data.header.date,
        
        // Subject
        subject = subject,
        
        // Recipient
        recipientLines = recipientLines,
        salutation = data.body.opening,
        
        // Body content
        opening = data.body.opening,
        paragraphs = data.body.paragraphs,
        closing = data.body.closing,
        
        // Signature
        farewell = data.signature.farewell,
        signatureName = data.signature.name,
        
        // Metadata
        templateId = data.metadata.templateId,
        language = data.metadata.language
    )
}

/**
 * Create a default structured letter data template
 * Kotlin equivalent of createDefaultStructuredLetterData from web
 */
fun createDefaultStructuredLetterData(
    templateId: String = "professional-classic",
    language: String = "id"
): StructuredLetterData {
    val now = kotlinx.datetime.Clock.System.now().toString()
    
    return StructuredLetterData(
        metadata = LetterMetadata(
            generatedAt = now,
            lastModified = now,
            templateId = templateId,
            language = language
        ),
        header = LetterHeader(
            date = if (language == "id") {
                // For now, using simple format - can be enhanced with platform-specific date formatting
                "Jakarta, ${kotlinx.datetime.Clock.System.now().toString().split('T')[0]}"
            } else {
                kotlinx.datetime.Clock.System.now().toString().split('T')[0]
            }
        ),
        subject = LetterSubject(
            prefix = if (language == "id") "Perihal: Lamaran Pekerjaan sebagai" else "Subject: Job Application for",
            position = ""
        ),
        recipient = LetterRecipient(
            salutation = if (language == "id") "Yth." else "Dear",
            title = if (language == "id") "Bapak/Ibu Bagian Sumber Daya Manusia" else "Hiring Manager"
        ),
        body = LetterBody(
            opening = if (language == "id") "Dengan hormat," else "Dear Sir/Madam,",
            paragraphs = emptyList(),
            closing = if (language == "id") {
                "Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih."
            } else {
                "Thank you for your time and consideration."
            }
        ),
        signature = LetterSignature(
            farewell = if (language == "id") "Hormat saya," else "Sincerely,",
            name = ""
        )
    )
}

/**
 * Convert template data to plain text format
 * Kotlin equivalent of convertTemplateDataToPlainText from web
 */
fun convertTemplateDataToPlainText(templateData: LetterTemplateData): String {
    val sections = mutableListOf<String>()
    
    // Add date
    if (templateData.date.isNotEmpty()) {
        sections.add(templateData.date)
    }
    
    // Add subject
    if (templateData.subject.isNotEmpty()) {
        sections.add(templateData.subject)
    }
    
    // Add recipient lines
    if (templateData.recipientLines.isNotEmpty()) {
        sections.add(templateData.recipientLines.joinToString("\n"))
    }
    
    // Add opening
    if (templateData.opening.isNotEmpty()) {
        sections.add(templateData.opening)
    }
    
    // Add paragraphs
    if (templateData.paragraphs.isNotEmpty()) {
        sections.add(templateData.paragraphs.joinToString("\n\n"))
    }
    
    // Add closing
    if (templateData.closing.isNotEmpty()) {
        sections.add(templateData.closing)
    }
    
    // Add signature block
    val signatureBlock = mutableListOf<String>()
    if (templateData.farewell.isNotEmpty()) {
        signatureBlock.add(templateData.farewell)
    }
    if (templateData.signatureName.isNotEmpty()) {
        signatureBlock.add(templateData.signatureName)
    }
    if (signatureBlock.isNotEmpty()) {
        sections.add(signatureBlock.joinToString("\n\n"))
    }
    
    // Join all sections with double line breaks
    return sections.joinToString("\n\n")
}

/**
 * Convert structured letter data directly to plain text format
 * Kotlin equivalent of convertStructuredDataToPlainText from web
 */
fun convertStructuredDataToPlainText(data: StructuredLetterData): String {
    val sections = mutableListOf<String>()
    
    // Add date
    if (data.header.date.isNotEmpty()) {
        sections.add(data.header.formattedDate ?: data.header.date)
    }
    
    // Add subject
    val subject = if (data.subject.prefix.isNotEmpty()) {
        "${data.subject.prefix} ${data.subject.position}"
    } else {
        data.subject.position
    }
    if (subject.isNotEmpty()) {
        sections.add(subject)
    }
    
    // Add recipient block
    val recipientLines = mutableListOf<String>()
    
    if (data.recipient.salutation.isNotEmpty() && data.recipient.title.isNotEmpty()) {
        recipientLines.add("${data.recipient.salutation} ${data.recipient.title}")
    }
    
    data.recipient.company?.let { company ->
        if (company.isNotEmpty()) {
            recipientLines.add(company)
        }
    }
    
    data.recipient.address?.let { address ->
        recipientLines.addAll(address.filter { it.isNotEmpty() })
    }
    
    if (recipientLines.isNotEmpty()) {
        sections.add(recipientLines.joinToString("\n"))
    }
    
    // Add opening
    if (data.body.opening.isNotEmpty()) {
        sections.add(data.body.opening)
    }
    
    // Add paragraphs
    if (data.body.paragraphs.isNotEmpty()) {
        sections.add(data.body.paragraphs.joinToString("\n\n"))
    }
    
    // Add closing
    if (data.body.closing.isNotEmpty()) {
        sections.add(data.body.closing)
    }
    
    // Add signature block
    val signatureBlock = mutableListOf<String>()
    
    if (data.signature.farewell.isNotEmpty()) {
        signatureBlock.add(data.signature.farewell)
    }
    
    if (data.signature.name.isNotEmpty()) {
        signatureBlock.add(data.signature.name)
    }
    
    if (signatureBlock.isNotEmpty()) {
        sections.add(signatureBlock.joinToString("\n\n"))
    }
    
    // Join all sections with double line breaks
    return sections.joinToString("\n\n")
}