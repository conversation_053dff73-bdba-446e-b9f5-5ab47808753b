package io.gigsta.data.network

import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.auth.Auth
// import io.github.jan.supabase.realtime.Realtime

object SupabaseClient {

    val client: SupabaseClient by lazy {
        createSupabaseClient(
            supabaseUrl = NetworkConfig.SUPABASE_URL,
            supabaseKey = NetworkConfig.SUPABASE_ANON_KEY
        ) {
            install(Postgrest)
            install(Auth)
            // install(Realtime) // Commented out for now
        }
    }
}
