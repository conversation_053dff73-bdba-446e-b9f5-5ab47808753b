package io.gigsta.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class ResumeHistoryItem(
    @SerialName("id")
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("data")
    val data: JsonElement? = null,
    @SerialName("status")
    val status: String? = null,
    @SerialName("pdf_url")
    val pdfUrl: String? = null,
    @SerialName("html")
    val htmlContent: String? = null,
    @SerialName("structured_data")
    val structuredData: JsonElement? = null,
    @SerialName("template_id")
    val templateId: String,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String,
    @SerialName("tokens_deducted")
    val tokensDeducted: Boolean = false
)