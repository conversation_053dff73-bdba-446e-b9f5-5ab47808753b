package io.gigsta.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class LetterHistoryItem(
    @SerialName("id")
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("plain_text")
    val plainText: String? = null,
    @SerialName("design_html")
    val designHtml: String? = null,
    @SerialName("template_id")
    val templateId: String,
    @SerialName("structured_data")
    val structuredData: JsonElement? = null,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String,
    @SerialName("status")
    val status: String? = null
)

@Serializable
data class LetterTemplate(
    val id: String,
    val name: String
)
