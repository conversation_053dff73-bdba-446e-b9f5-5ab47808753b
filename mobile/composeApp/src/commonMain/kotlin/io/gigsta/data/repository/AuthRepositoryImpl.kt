package io.gigsta.data.repository

import io.gigsta.data.network.SupabaseClient
import io.gigsta.domain.model.AuthState
import io.gigsta.domain.model.User
import io.gigsta.domain.repository.AuthRepository
import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.auth.providers.builtin.Email
import io.github.jan.supabase.auth.user.UserInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class AuthRepositoryImpl : AuthRepository {
    
    private val supabase = SupabaseClient.client
    private val auth = supabase.auth
    
    override val authState: Flow<AuthState> = auth.sessionStatus.map { sessionStatus ->
        try {
            when (sessionStatus) {
                is io.github.jan.supabase.auth.status.SessionStatus.Authenticated -> {
                    val user = mapUserInfoToUser(sessionStatus.session.user!!)
                    AuthState.Authenticated(user)
                }
                is io.github.jan.supabase.auth.status.SessionStatus.NotAuthenticated -> {
                    AuthState.Unauthenticated
                }
                is io.github.jan.supabase.auth.status.SessionStatus.Initializing -> {
                    AuthState.Loading
                }
                else -> {
                    AuthState.Error("Unknown authentication state")
                }
            }
        } catch (e: Exception) {
            AuthState.Error(e.message ?: "Authentication error")
        }
    }
    
    override suspend fun signUp(email: String, password: String): Result<User> {
        return try {
            auth.signUpWith(Email) {
                this.email = email
                this.password = password
            }
            
            val userInfo = auth.currentUserOrNull()
            if (userInfo != null) {
                val user = mapUserInfoToUser(userInfo)
                Result.success(user)
            } else {
                Result.failure(Exception("Failed to get user after signup"))
            }
        } catch (e: Exception) {
            Result.failure(Exception("Sign up failed: ${e.message}"))
        }
    }
    
    override suspend fun signIn(email: String, password: String): Result<User> {
        return try {
            auth.signInWith(Email) {
                this.email = email
                this.password = password
            }
            
            val userInfo = auth.currentUserOrNull()
            if (userInfo != null) {
                val user = mapUserInfoToUser(userInfo)
                Result.success(user)
            } else {
                Result.failure(Exception("Failed to get user after signin"))
            }
        } catch (e: Exception) {
            Result.failure(Exception("Sign in failed: ${e.message}"))
        }
    }
    
    override suspend fun signOut(): Result<Unit> {
        return try {
            auth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(Exception("Sign out failed: ${e.message}"))
        }
    }
    
    override suspend fun getCurrentUser(): User? {
        return try {
            val userInfo = auth.currentUserOrNull()
            userInfo?.let { mapUserInfoToUser(it) }
        } catch (e: Exception) {
            null
        }
    }
    
    override suspend fun refreshSession(): Result<User> {
        return try {
            auth.refreshCurrentSession()
            val userInfo = auth.currentUserOrNull()
            if (userInfo != null) {
                val user = mapUserInfoToUser(userInfo)
                Result.success(user)
            } else {
                Result.failure(Exception("Failed to refresh session"))
            }
        } catch (e: Exception) {
            Result.failure(Exception("Session refresh failed: ${e.message}"))
        }
    }
    
    private fun mapUserInfoToUser(userInfo: UserInfo): User {
        return User(
            id = userInfo.id,
            email = userInfo.email ?: "",
            createdAt = userInfo.createdAt?.toString(),
            lastSignInAt = userInfo.lastSignInAt?.toString()
        )
    }
}
