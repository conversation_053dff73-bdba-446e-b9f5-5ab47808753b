package io.gigsta.data.datasource

import io.gigsta.data.model.EmailHistoryItem
import io.gigsta.data.network.SupabaseClient
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Columns
import io.github.jan.supabase.postgrest.query.Order

class EmailDataSource {
    
    private val supabase = SupabaseClient.client
    
    suspend fun getEmailHistory(userId: String): Result<List<EmailHistoryItem>> {
        return try {
            val result = supabase
                .from("emails")
                .select(columns = Columns.ALL) {
                    filter {
                        eq("user_id", userId)
                    }
                    order("created_at", order = Order.DESCENDING)
                }
                .decodeList<EmailHistoryItem>()
            Result.success(result)
        } catch (e: Exception) {
            println("Error fetching email history: ${e.message}")
            Result.failure(Exception("Failed to fetch email history: ${e.message}"))
        }
    }

    suspend fun getEmailById(id: String): Result<EmailHistoryItem?> {
        return try {
            val result = supabase
                .from("emails")
                .select(columns = Columns.ALL) {
                    filter {
                        eq("id", id)
                    }
                }
                .decodeSingle<EmailHistoryItem>()
            Result.success(result)
        } catch (e: Exception) {
            println("Error fetching email by id: ${e.message}")
            Result.failure(Exception("Failed to fetch email: ${e.message}"))
        }
    }
}
