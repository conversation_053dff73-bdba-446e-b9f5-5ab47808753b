# History Function Integration

This document describes the integration of history functionality from the web application to the mobile HomeScreen.

## Overview

The mobile app now fetches real history data from the same Supabase backend used by the web application, displaying:
- Resume history (CV Builder)
- Application letter history
- Email application history
- Job match history (placeholder for future implementation)

## Architecture

### Data Layer
- **Data Models**: Kotlin data classes that match web TypeScript interfaces
  - `ResumeHistoryItem` - matches web `ResumeHistoryItem`
  - `LetterHistoryItem` - matches web `LetterHistoryItem`
  - `EmailHistoryItem` - matches web `EmailHistoryItem`

- **Data Sources**: Handle API communication with Supabase
  - `ResumeDataSource` - fetches resume data
  - `LetterDataSource` - fetches letter data
  - `EmailDataSource` - fetches email data

- **Repository**: `HistoryRepositoryImpl` - transforms API data to domain models

### Network Layer
- **Supabase Client**: Configured to connect to the same backend as web app
- **Error Handling**: Proper Result types for network operations
- **Configuration**: Centralized network configuration

## Setup Instructions

### 1. Configure Supabase Credentials

Update `mobile/composeApp/src/commonMain/kotlin/io/gigsta/data/network/NetworkConfig.kt`:

```kotlin
object NetworkConfig {
    const val SUPABASE_URL = "YOUR_ACTUAL_SUPABASE_URL"
    const val SUPABASE_ANON_KEY = "YOUR_ACTUAL_ANON_KEY"
}
```

### 2. User Authentication

Currently using a placeholder user ID. You'll need to implement proper authentication:

```kotlin
// In HistoryRepositoryImpl.kt
private val currentUserId = "test-user-id" // Replace with actual user session
```

### 3. Template Name Resolution

The app includes placeholder template name resolution. Implement actual template lookup:

```kotlin
private fun getResumeTemplateName(templateId: String): String {
    // TODO: Implement actual template lookup from web app's template system
    return "Professional Template"
}
```

## Features Implemented

### Enhanced UI Components
- **Type-specific Icons**: Different icons and colors for each history type
- **Status Indicators**: Shows "In Progress" for incomplete items
- **Template Information**: Displays template names for resumes and letters
- **Rich Content Display**: Shows titles, subtypes, dates, and progress

### Error Handling
- Network request failures are handled gracefully
- Empty states are shown when no data is available
- Error messages are logged for debugging

### Data Synchronization
- Fetches data from the same Supabase tables as the web app
- Applies the same filtering logic (status="done", tokens_deducted=true, etc.)
- Orders by creation date (newest first)

## Next Steps

1. **Authentication Integration**: Connect to your app's authentication system
2. **Template System**: Integrate with the web app's template resolution
3. **Real-time Updates**: Add Supabase realtime subscriptions for live updates
4. **Caching**: Implement local caching for offline support
5. **Job Match Integration**: Implement job matching data source when available

## Dependencies Added

```toml
# In gradle/libs.versions.toml
ktor = "3.0.2"
kotlinx-serialization = "1.7.3"
kotlinx-datetime = "0.6.1"
supabase = "3.0.2"
```

## File Structure

```
mobile/composeApp/src/commonMain/kotlin/io/gigsta/
├── data/
│   ├── datasource/
│   │   ├── ResumeDataSource.kt
│   │   ├── LetterDataSource.kt
│   │   └── EmailDataSource.kt
│   ├── model/
│   │   ├── ResumeHistoryItem.kt
│   │   ├── LetterHistoryItem.kt
│   │   └── EmailHistoryItem.kt
│   ├── network/
│   │   ├── SupabaseClient.kt
│   │   └── NetworkConfig.kt
│   └── repository/
│       └── HistoryRepositoryImpl.kt (updated)
├── domain/model/
│   └── HistoryItem.kt (enhanced)
└── presentation/home/
    └── HomeScreen.kt (enhanced UI)
```
